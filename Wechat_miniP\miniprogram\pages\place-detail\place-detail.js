// place-detail.js
Component({
  data: {
    placeId: 0,
    placeData: null,
    loading: true,
    isFavorited: false,
    relatedPlaces: [],
    userId: 1 // 临时用户ID，实际应该从全局状态获取
  },

  lifetimes: {
    attached: function() {
      // 获取页面参数
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = currentPage.options;

      if (options.id) {
        this.setData({
          placeId: parseInt(options.id)
        });
        this.loadPlaceDetail();
      } else {
        this.setData({ loading: false });
        wx.showToast({
          title: '景点ID无效',
          icon: 'none'
        });
      }
    }
  },

  methods: {
    // 加载景点详情
    loadPlaceDetail: function() {
      const that = this;
      this.setData({ loading: true });

      wx.request({
        url: 'http://localhost:5000/api/locations/' + this.data.placeId,
        method: 'GET',
        success: function(res) {
          // 修复响应格式判断：后端返回 code: 0 表示成功
          if (res.data.code === 0 && res.data.data) {
            const placeData = res.data.data;
            that.setData({
              placeData: placeData,
              loading: false,
              placeImageUrl: that.getPlaceImageUrl(placeData.image_url),
              placeTypeText: that.getPlaceTypeText(placeData.type)
            });

            // 加载相关景点
            that.loadRelatedPlaces();

            // 检查收藏状态
            that.checkFavoriteStatus();
          } else {
            console.error('加载景点详情失败:', res.data);
            that.setData({ loading: false });
            wx.showToast({
              title: res.data.message || '加载失败',
              icon: 'none'
            });
          }
        },
        fail: function(error) {
          console.error('加载景点详情失败:', error);
          that.setData({ loading: false });
          wx.showToast({
            title: '网络错误',
            icon: 'none'
          });
        }
      });
    },

    // 加载相关景点
    loadRelatedPlaces: function() {
      const that = this;

      // 获取所有景点，然后筛选出相关的
      wx.request({
        url: 'http://localhost:5000/api/locations',
        method: 'GET',
        success: function(res) {
          if (res.data.code === 0 && res.data.data) {
            // 过滤掉当前景点，取前5个作为相关景点
            const allPlaces = res.data.data;
            const relatedPlaces = allPlaces
              .filter(place => place.location_id !== that.data.placeId)
              .slice(0, 5);

            that.setData({ relatedPlaces: relatedPlaces });
          }
        },
        fail: function(error) {
          console.error('加载相关景点失败:', error);
        }
      });
    },

    // 检查收藏状态
    checkFavoriteStatus: function() {
      // 这里可以添加检查收藏状态的逻辑
      // 暂时设为false
      this.setData({ isFavorited: false });
    },

    // 获取景点类型文本
    getPlaceTypeText: function(type) {
      const types = {
        0: '学校',
        1: '景点'
      };
      return types[type] || '未知';
    },

    // 获取景点图片URL
    getPlaceImageUrl: function(imageUrl) {
      if (!imageUrl) {
        return 'http://localhost:5000/uploads/locations/default_location.jpg';
      }

      if (imageUrl.startsWith('http')) {
        return imageUrl;
      } else {
        return 'http://localhost:5000' + imageUrl;
      }
    },

    // 获取位置图片URL
    getLocationImageUrl: function(imageUrl) {
      if (!imageUrl) {
        return 'http://localhost:5000/uploads/locations/default_location.jpg';
      }

      if (imageUrl.startsWith('http')) {
        return imageUrl;
      } else {
        return 'http://localhost:5000' + imageUrl;
      }
    },

    // 切换收藏状态
    toggleFavorite: function() {
      this.setData({
        isFavorited: !this.data.isFavorited
      });

      wx.showToast({
        title: this.data.isFavorited ? '已收藏' : '已取消收藏',
        icon: 'success'
      });
    },

    // 分享景点
    sharePlace: function() {
      wx.showShareMenu({
        withShareTicket: true
      });
    },

    // 分享给朋友
    onShareAppMessage: function() {
      return {
        title: this.data.placeData ? this.data.placeData.name : '景点详情',
        path: '/pages/place-detail/place-detail?id=' + this.data.placeId,
        imageUrl: this.data.placeImageUrl
      };
    },

    // 查看图片
    previewImage: function() {
      if (this.data.placeData) {
        wx.previewImage({
          urls: [this.data.placeImageUrl]
        });
      }
    },

    // 导航到景点
    navigateToPlace: function() {
      if (this.data.placeData) {
        // 这里可以集成地图导航功能
        wx.showModal({
          title: '导航提示',
          content: '是否打开地图应用进行导航？',
          success: function(res) {
            if (res.confirm) {
              wx.showToast({
                title: '导航功能开发中',
                icon: 'none'
              });
            }
          }
        });
      }
    },

    // 查看相关景点
    onRelatedPlaceTap: function(e) {
      const id = e.currentTarget.dataset.id;
      wx.redirectTo({
        url: '/pages/place-detail/place-detail?id=' + id
      });
    },

    // 返回
    goBack: function() {
      wx.navigateBack();
    }
  }
})
