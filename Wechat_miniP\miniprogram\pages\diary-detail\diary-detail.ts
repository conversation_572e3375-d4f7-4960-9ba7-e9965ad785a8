// diary-detail.ts
interface DiaryData {
  article_id: number;
  title: string;
  content: string;
  location?: string;
  created_at: string;
  popularity: number;
  like_count: number;
  image_url?: string;
  image_url_2?: string;
  image_url_3?: string;
  image_url_4?: string;
  image_url_5?: string;
  image_url_6?: string;
  tags?: string;
}

Component({
  data: {
    diaryId: 0,
    diaryData: null as DiaryData | null,
    loading: true,
    imageList: [] as string[],
    tagList: [] as string[],
    isLiked: false,
    isFavorited: false,
    userId: 1 // 临时用户ID
  },

  lifetimes: {
    attached() {
      // 获取页面参数
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = currentPage.options;

      if (options.id) {
        this.setData({
          diaryId: parseInt(options.id)
        });
        this.loadDiaryDetail();
      } else {
        this.setData({ loading: false });
      }
    }
  },

  methods: {
    // 加载日记详情
    async loadDiaryDetail() {
      this.setData({ loading: true });

      try {
        const response = await this.requestDiaryDetail(this.data.diaryId);

        // 修复响应格式判断：后端返回 code: 0 表示成功
        if (response.code === 0 && response.data) {
          const diaryData = response.data;

          // 处理图片列表
          const imageList = [
            diaryData.image_url,
            diaryData.image_url_2,
            diaryData.image_url_3,
            diaryData.image_url_4,
            diaryData.image_url_5,
            diaryData.image_url_6
          ].filter(url => url && url.trim());

          // 处理标签列表
          let tagList: string[] = [];
          if (diaryData.tags) {
            try {
              tagList = JSON.parse(diaryData.tags);
            } catch (e) {
              tagList = [];
            }
          }

          this.setData({
            diaryData: {
              ...diaryData,
              created_at: this.formatDate(diaryData.created_at)
            },
            imageList,
            tagList,
            loading: false
          });

          // 检查点赞和收藏状态
          this.checkLikeStatus();
          this.checkFavoriteStatus();
        } else {
          console.error('加载日记详情失败:', response);
          this.setData({ loading: false });
          wx.showToast({
            title: response.message || '加载失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('加载日记详情失败:', error);
        this.setData({ loading: false });
      }
    },

    // 请求日记详情
    requestDiaryDetail(articleId: number) {
      return new Promise((resolve) => {
        wx.request({
          url: `http://localhost:5000/api/articles/${articleId}`,
          method: 'GET',
          success: (res) => {
            resolve(res.data);
          },
          fail: (error) => {
            resolve({ code: 1, message: '网络错误', error });
          }
        });
      });
    },

    // 格式化日期
    formatDate(dateString: string): string {
      if (!dateString) return '';
      const date = new Date(dateString);
      return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`;
    },

    // 预览图片
    previewImage(e: any) {
      const index = e.currentTarget.dataset.index;
      wx.previewImage({
        current: this.data.imageList[index],
        urls: this.data.imageList
      });
    },

    // 检查点赞状态
    async checkLikeStatus() {
      try {
        const response = await this.requestCheckLike();
        if (response.success) {
          this.setData({
            isLiked: response.data.is_liked || false
          });
        }
      } catch (error) {
        console.error('检查点赞状态失败:', error);
      }
    },

    // 请求检查点赞状态
    requestCheckLike() {
      return new Promise((resolve) => {
        wx.request({
          url: 'http://localhost:5000/api/article_like/check',
          method: 'POST',
          data: {
            user_id: this.data.userId,
            article_id: this.data.diaryId
          },
          success: (res) => {
            resolve(res.data);
          },
          fail: (error) => {
            resolve({ success: false, error });
          }
        });
      });
    },

    // 检查收藏状态
    async checkFavoriteStatus() {
      // 这里可以添加检查收藏状态的逻辑
      // 暂时设为false
      this.setData({ isFavorited: false });
    },

    // 切换点赞
    async toggleLike() {
      try {
        const response = this.data.isLiked
          ? await this.requestUnlike()
          : await this.requestLike();

        if (response.success) {
          this.setData({
            isLiked: !this.data.isLiked
          });

          // 更新点赞数
          if (this.data.diaryData) {
            const newLikeCount = this.data.isLiked
              ? this.data.diaryData.like_count + 1
              : this.data.diaryData.like_count - 1;

            this.setData({
              'diaryData.like_count': Math.max(0, newLikeCount)
            });
          }
        }
      } catch (error) {
        console.error('点赞操作失败:', error);
        wx.showToast({
          title: '操作失败',
          icon: 'none'
        });
      }
    },

    // 请求点赞
    requestLike() {
      return new Promise((resolve) => {
        wx.request({
          url: 'http://localhost:5000/api/article_like',
          method: 'POST',
          data: {
            user_id: this.data.userId,
            article_id: this.data.diaryId
          },
          success: (res) => {
            resolve(res.data);
          },
          fail: (error) => {
            resolve({ success: false, error });
          }
        });
      });
    },

    // 请求取消点赞
    requestUnlike() {
      return new Promise((resolve) => {
        wx.request({
          url: 'http://localhost:5000/api/article_like/unlike',
          method: 'POST',
          data: {
            user_id: this.data.userId,
            article_id: this.data.diaryId
          },
          success: (res) => {
            resolve(res.data);
          },
          fail: (error) => {
            resolve({ success: false, error });
          }
        });
      });
    },

    // 切换收藏
    toggleFavorite() {
      this.setData({
        isFavorited: !this.data.isFavorited
      });

      wx.showToast({
        title: this.data.isFavorited ? '已收藏' : '已取消收藏',
        icon: 'success'
      });
    },

    // 分享文章
    shareArticle() {
      wx.showShareMenu({
        withShareTicket: true
      });
    },

    // 返回
    goBack() {
      wx.navigateBack();
    }
  }
})
